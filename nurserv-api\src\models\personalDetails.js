const { pool } = require('../config/database');

class PersonalDetails {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.date_of_birth = data.date_of_birth ? new Date(data.date_of_birth).toLocaleDateString("en-CA") : null;
    this.gender = data.gender;
    this.emergency_contact = data.emergency_contact;
    this.nuid = data.nuid;
    this.total_years_of_exp = data.total_years_of_exp;
    this.profile_verified = data.profile_verified;
    
    // Fixed: Properly handle service_provide array/string conversion
    if (data.service_provide) {
      if (typeof data.service_provide === 'string') {
        try {
          // If it's a JSON string, parse it
          this.service_provide = JSON.parse(data.service_provide);
        } catch (e) {
          // If parsing fails, treat as comma-separated string
          this.service_provide = data.service_provide.split(',').map(s => s.trim()).filter(s => s);
        }
      } else if (Array.isArray(data.service_provide)) {
        this.service_provide = data.service_provide;
      } else {
        this.service_provide = [];
      }
    } else {
      this.service_provide = [];
    }
    
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    
    // newly Added
    this.phone_number = data.phone_number;
    this.user_name = data.user_name;
    this.given_name = data.given_name;
    this.family_name = data.family_name;
    this.email = data.email;
    this.longitude = data.longitude;
    this.latitude = data.latitude;
    this.address = data.address;
    this.about = data.about;
  }

  static async create(detailsData) {
    try {
      // Format date fields properly
      const formattedData = { ...detailsData };
      
      // Format date_of_birth to MySQL date format (YYYY-MM-DD) if it exists
      if (formattedData.date_of_birth) {
        const dateObj = new Date(formattedData.date_of_birth + ' UTC');
        if (!isNaN(dateObj.getTime())) {
          formattedData.date_of_birth = dateObj.toISOString().split('T')[0];
        }
      }
      
      // Handle service_provide array - convert to comma-separated string for storage
      if (formattedData.service_provide) {
        if (Array.isArray(formattedData.service_provide)) {
          // Store as comma-separated string
          formattedData.service_provide = formattedData.service_provide.join(', ');
        }
      }
      
      // First, determine which fields actually exist in the personal_details table
      const [columns] = await pool.query(
        `SHOW COLUMNS FROM personal_details`
      );
      
      const validColumns = columns.map(col => col.Field);
      
      // Prepare only valid columns and values
      const fields = [];
      const values = [];
      const placeholders = [];
      
      for (const [key, value] of Object.entries(formattedData)) {
        if (validColumns.includes(key)) {
          fields.push(key);
          values.push(value);
          placeholders.push('?');
        }
      }
      
      // Always include user_id and timestamps
      if (!fields.includes('user_id') && formattedData.user_id) {
        fields.push('user_id');
        values.push(formattedData.user_id);
        placeholders.push('?');
      }
      
      if (!fields.includes('created_at')) {
        fields.push('created_at');
        placeholders.push('CONVERT_TZ(NOW(), "UTC", "+05:30")');
      }

      if (!fields.includes('updated_at')) {
        fields.push('updated_at');
        placeholders.push('CONVERT_TZ(NOW(), "UTC", "+05:30")');
      }
      
      // Start a transaction
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // Execute the query with only valid columns
        const [result] = await connection.query(
          `INSERT INTO personal_details (${fields.join(', ')})
           VALUES (${placeholders.join(', ')})`,
          values
        );

        // Update nurse_onboard_complete flag in users table
        await connection.query(
          `UPDATE users 
           SET nurse_onboard_complete = true, 
               updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30") 
           WHERE cognito_id = ?`,
          [formattedData.user_id]
        );

        // Commit the transaction
        await connection.commit();

        return { id: result.insertId, ...detailsData };
      } catch (error) {
        // Rollback the transaction in case of error
        await connection.rollback();
        throw error;
      } finally {
        // Release the connection
        connection.release();
      }
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create personal details');
    }
  }

  static async findByUserId(userId) {
    try {
      const [rows] = await pool.query(
        `SELECT users.*, personal_details.*
         FROM users
         LEFT JOIN personal_details ON users.cognito_id = personal_details.user_id
         WHERE users.cognito_id = ?`,
        [userId]
      );
      
      if (rows.length > 0) {
        const data = rows[0];
        
        // Handle service_provide parsing
        if (data.service_provide) {
          if (typeof data.service_provide === 'string') {
            // Check if it's JSON string first
            if (data.service_provide.startsWith('[') || data.service_provide.startsWith('{')) {
              try {
                data.service_provide = JSON.parse(data.service_provide);
              } catch (e) {
                // If JSON parsing fails, treat as comma-separated string
                data.service_provide = data.service_provide.split(',').map(s => s.trim()).filter(s => s);
              }
            } else {
              // Treat as comma-separated string
              data.service_provide = data.service_provide.split(',').map(s => s.trim()).filter(s => s);
            }
          }
        }
        
        return new PersonalDetails(data);
      }
      return null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch personal details');
    }
  }

  static async personalDetailsUpdate(userId, updateData) {
    try {
      // Format date fields properly
      const formattedData = { ...updateData };
      
      // Format date_of_birth to MySQL date format (YYYY-MM-DD) if it exists
      if (formattedData.date_of_birth) {
        const dateObj = new Date(formattedData.date_of_birth);
        if (!isNaN(dateObj.getTime())) {
          formattedData.date_of_birth = dateObj.toISOString().split('T')[0];
        }
      }
      
      // Handle service_provide array - convert to comma-separated string
      if (formattedData.service_provide) {
        if (Array.isArray(formattedData.service_provide)) {
          formattedData.service_provide = formattedData.service_provide.join(', ');
        }
      }
      
      // First, check if a record exists in personal_details for this user
      const existingRecord = await this.findByUserId(userId);
      
      if (!existingRecord || !existingRecord.id) {
        // If no record exists in personal_details, create one
        return await this.create({
          user_id: userId,
          ...formattedData
        });
      }
      
      // Fields that can be updated in users table
      const userFields = [
        'given_name',
        'family_name',
        'email',
        'phone_number',
        'latitude',
        'longitude',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'nurse_onboard_complete'
      ];

      // Fields that can be updated in personal_details table
      const personalDetailsFields = [
        'date_of_birth',
        'gender',
        'emergency_contact',
        'total_years_of_exp',
        'service_provide',
        'about',
        'nuid',
      ];

      // Prepare updates for users table
      const userUpdates = [];
      const userValues = [];
      for (const [key, value] of Object.entries(formattedData)) {
        if (userFields.includes(key)) {
          userUpdates.push(`${key} = ?`);
          userValues.push(value);
        }
      }

      // Prepare updates for personal_details table
      const personalDetailsUpdates = [];
      const personalDetailsValues = [];
      for (const [key, value] of Object.entries(formattedData)) {
        if (personalDetailsFields.includes(key)) {
          personalDetailsUpdates.push(`${key} = ?`);
          personalDetailsValues.push(value);
        }
      }

      // Start a transaction
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // Update users table if there are changes
        if (userUpdates.length > 0) {
          userValues.push(userId); // Add userId
          const [userResult] = await connection.query(
            `UPDATE users
             SET ${userUpdates.join(', ')}, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30")
             WHERE cognito_id = ?`,
            userValues
          );
          if (userResult.affectedRows === 0) {
            throw new Error('Failed to update user record');
          }
        }

        // Update personal_details table if there are changes
        if (personalDetailsUpdates.length > 0) {
          personalDetailsValues.push(userId); // Add userId
          const [personalDetailsResult] = await connection.query(
            `UPDATE personal_details
             SET ${personalDetailsUpdates.join(', ')}, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30")
             WHERE user_id = ?`,
            personalDetailsValues
          );
          if (personalDetailsResult.affectedRows === 0) {
            throw new Error('Failed to update personal details record');
          }
        }

        // Commit the transaction
        await connection.commit();

        // Return the updated record
        return await this.findByUserId(userId);
      } catch (error) {
        // Rollback the transaction in case of error
        await connection.rollback();
        throw error;
      } finally {
        // Release the connection
        connection.release();
      }
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update personal details');
    }
  }

  static async professionalDetailsUpdate(userId, updateData) {
    try {
      // Format date fields properly
      const formattedData = { ...updateData };
      
      // Handle service_provide array - convert to comma-separated string
      if (formattedData.service_provide) {
        if (Array.isArray(formattedData.service_provide)) {
          formattedData.service_provide = formattedData.service_provide.join(', ');
        }
      }
      
      // First, check if a record exists in personal_details for this user
      const existingRecord = await this.findByUserId(userId);
      
      if (!existingRecord || !existingRecord.id) {
        // If no record exists in personal_details, create one
        return await this.create({
          user_id: userId,
          ...formattedData
        });
      }
      
      // If record exists, update it
      const allowedFields = [
        'given_name', 'family_name', 'email', 'date_of_birth',
        'gender', 'phone_number', 'address', 'longitude', 'latitude',
        'emergency_contact', 'total_years_of_exp', 
        'service_provide', 'about', 'nurse_onboard_complete'
      ];

      const updates = [];
      const values = [];

      for (const [key, value] of Object.entries(formattedData)) {
        if (allowedFields.includes(key)) {
          updates.push(`${key} = ?`);
          values.push(value);
        }
      }

      if (updates.length === 0) {
        return existingRecord; // Return existing record if no updates needed
      }

      values.push(userId);

      // Update personal_details table
      const [result] = await pool.query(
        `UPDATE personal_details
         SET ${updates.join(', ')}, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30") 
         WHERE user_id = ?`,
        values
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return this.findByUserId(userId);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update personal details');
    }
  }

  static async delete(userId) {
    try {
      const [result] = await pool.query(
        'DELETE FROM personal_details WHERE user_id = ?',
        [userId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete personal details');
    }
  }
}

module.exports = PersonalDetails;